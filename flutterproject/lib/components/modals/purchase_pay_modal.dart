import 'package:flutter/material.dart';

class PaymentDialog extends StatefulWidget {
  final double amount;
  final VoidCallback? onClose;
  final VoidCallback? onPay;

  const PaymentDialog({
    super.key,
    required this.amount,
    this.onClose,
    this.onPay,
  });

  @override
  State<PaymentDialog> createState() => _PaymentDialogState();

  // 静态方法用于显示底部弹窗
  static Future<void> show(
    BuildContext context, {
    required double amount,
    VoidCallback? onClose,
    VoidCallback? onPay,
  }) {
    return showModalBottomSheet<void>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => PaymentDialog(
        amount: amount,
        onClose: onClose ?? () => Navigator.of(context).pop(),
        onPay: onPay,
      ),
    );
  }
}

class _PaymentDialogState extends State<PaymentDialog> {
  int _selectedPaymentMethod = 0; // 0: 支付宝, 1: 微信, 2: MPAY

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.white,
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 顶部拖拽指示器
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 36,
              height: 4,
              decoration: BoxDecoration(
                color: const Color(0xFFE5E7EB),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // 顶部金额和关闭按钮
            Container(
              padding: const EdgeInsets.fromLTRB(24, 20, 16, 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      '¥${widget.amount.toStringAsFixed(2)}',
                      style: const TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF2D3748),
                        letterSpacing: -0.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  GestureDetector(
                    onTap: widget.onClose,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      child: const Icon(
                        Icons.close,
                        size: 24,
                        color: Color(0xFF9CA3AF),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // 支付方式列表
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                children: [
                  _buildPaymentOption(
                    index: 0,
                    icon: _buildAlipayIcon(),
                    title: '支付宝支付',
                  ),
                  const SizedBox(height: 16),
                  _buildPaymentOption(
                    index: 1,
                    icon: _buildWechatIcon(),
                    title: '微信支付',
                  ),
                  const SizedBox(height: 16),
                  _buildPaymentOption(
                    index: 2,
                    icon: _buildMpayIcon(),
                    title: 'MPAY',
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 32),
            
            // 立即支付按钮
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
              child: SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: widget.onPay,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF9F7AEA),
                    foregroundColor: Colors.white,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                  ),
                  child: const Text(
                    '立即支付',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      letterSpacing: 0.5,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentOption({
    required int index,
    required Widget icon,
    required String title,
  }) {
    final isSelected = _selectedPaymentMethod == index;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedPaymentMethod = index;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            SizedBox(
              width: 32,
              height: 32,
              child: icon,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  color: Color(0xFF2D3748),
                ),
              ),
            ),
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? const Color(0xFF9F7AEA) : const Color(0xFFD1D5DB),
                  width: 2,
                ),
                color: isSelected ? const Color(0xFF9F7AEA) : Colors.transparent,
              ),
              child: isSelected
                  ? const Icon(
                      Icons.check,
                      size: 12,
                      color: Colors.white,
                    )
                  : null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAlipayIcon() {
    return Container(
      width: 32,
      height: 32,
      decoration: const BoxDecoration(
        color: Color(0xFF1677FF),
        shape: BoxShape.circle,
      ),
      child: const Icon(
        Icons.account_balance_wallet,
        color: Colors.white,
        size: 18,
      ),
    );
  }

  Widget _buildWechatIcon() {
    return Container(
      width: 32,
      height: 32,
      decoration: const BoxDecoration(
        color: Color(0xFF07C160),
        shape: BoxShape.circle,
      ),
      child: const Icon(
        Icons.chat_bubble,
        color: Colors.white,
        size: 18,
      ),
    );
  }

  Widget _buildMpayIcon() {
    return Container(
      width: 32,
      height: 32,
      decoration: const BoxDecoration(
        color: Color(0xFFFF6B35),
        shape: BoxShape.circle,
      ),
      child: const Center(
        child: Text(
          'M',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}

// 使用示例
class PaymentDialogDemo extends StatelessWidget {
  const PaymentDialogDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            PaymentDialog.show(
              context,
              amount: 52.70,
              onPay: () {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('支付功能待实现')),
                );
              },
            );
          },
          child: const Text('显示支付弹窗'),
        ),
      ),
    );
  }
}